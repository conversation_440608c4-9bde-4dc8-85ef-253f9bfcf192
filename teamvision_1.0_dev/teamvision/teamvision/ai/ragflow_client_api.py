# coding=utf-8
"""
RAGFlow Agent客户端 - HTTP API版本

支持两种工作模式：
1. Chat模式：基于知识库的对话式AI，适合复杂的测试用例生成
2. Agent模式：基于预配置Agent的对话，适合特定场景的快速响应

使用示例：
    # Chat模式
    ragflow_chat_client = RAGFlowClient(mode="chat")
    response = chat_client.ask("请帮我生成登录功能的测试用例")

    # Agent模式
    ragflow_agent_client = RAGFlowClient(mode="agent")
    response = agent_client.ask("请帮我生成测试用例")
"""

import requests
import json
from typing import Dict, List, Optional, Any, Union
from django.conf import settings
from pathlib import Path
from gatesidelib.common.simplelogger import SimpleLogger


class RAGFlowClient:
    """RAGFlow客户端 - 支持chat和agent两种模式"""

    def __init__(self, mode: str = "chat"):
        """
        初始化RAGFlow客户端

        Args:
            mode: 工作模式，支持 'chat' 或 'agent'
        """
        if mode not in ['chat', 'agent']:
            raise ValueError("mode 必须是 'chat' 或 'agent'")

        self.mode = mode
        self.base_url = getattr(settings, 'RAGFLOW_BASE_URL', 'http://localhost:9380')
        self.api_key = getattr(settings, 'RAGFLOW_API_KEY', '')
        self.dataset_name = getattr(settings, 'RAGFLOW_DATASET_NAME', 'testcase_generation')
        self.chat_name = getattr(settings, 'RAGFLOW_CHAT_NAME', 'TestCase Assistant')
        self.agent_id = getattr(settings, 'RAGFLOW_AGENT_ID', '')
        self.timeout = getattr(settings, 'RAGFLOW_TIMEOUT', 30)

        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}',
        }

        # 初始化模式相关资源
        self.dataset_id = None
        self.chat_id = None
        self.agent_session_id = None

        # 根据模式初始化相应资源
        self._initialize_resources()

    def _initialize_resources(self):
        """根据模式初始化相应资源"""
        try:
            if self.mode == "chat":
                self._initialize_chat_resources("请介绍一下您的功能")
            elif self.mode == "agent":
                self._initialize_agent_resources()

            SimpleLogger.info(f"RAGFlow {self.mode} 模式资源初始化完成")
        except Exception as e:
            SimpleLogger.exception(f"RAGFlow {self.mode} 模式资源初始化失败: {str(e)}")
            # 不抛出异常，允许后续手动重试

    def _initialize_chat_resources(self, prompt: str):
        """初始化chat模式资源"""
        # 获取或创建数据集
        self.dataset_id = self._get_or_create_dataset()

        # 获取或创建聊天助手
        self.chat_id = self._get_or_create_chat_assistant(prompt=prompt)

    def _initialize_agent_resources(self):
        """初始化agent模式资源"""
        if not self.agent_id:
            raise Exception("Agent模式需要配置RAGFLOW_AGENT_ID")

        # 验证agent是否存在
        try:
            agent_data = self._make_request('GET', f'/api/v1/agents/{self.agent_id}')
            SimpleLogger.info(f"Agent验证成功: {agent_data}")
        except Exception as e:
            raise Exception(f"未找到ID为 {self.agent_id} 的Agent: {str(e)}")

        # 创建agent会话
        self.agent_session_id = self._create_agent_session()
        SimpleLogger.info(f"Agent会话创建成功: {self.agent_session_id}")

    def _get_or_create_dataset(self) -> str:
        """获取或创建数据集"""
        try:
            # 尝试获取现有数据集
            datasets_data = self._make_request('GET', '/api/v1/datasets', {'name': self.dataset_name})
            datasets = datasets_data.get('data', [])

            if datasets:
                SimpleLogger.info(f"找到现有数据集: {self.dataset_name}")
                return datasets[0]['id']

            # 创建新数据集
            SimpleLogger.info(f"创建新数据集: {self.dataset_name}")
            dataset_data = self._make_request('POST', '/api/v1/datasets', {
                'name': self.dataset_name,
                'description': '用于测试用例生成的知识库',
                'chunk_method': 'qa',  # 使用Q&A模式适合测试用例生成
                'embedding_model': 'BAAI/bge-large-zh-v1.5@BAAI'
            })
            return dataset_data.get('data', {}).get('id', '')

        except Exception as e:
            SimpleLogger.exception(f"数据集操作失败: {str(e)}")
            raise Exception(f"数据集操作失败: {str(e)}")

    def _get_or_create_chat_assistant(self, prompt: str) -> str:
        """获取或创建聊天助手"""
        try:
            # 尝试获取现有聊天助手
            chats_data = self._make_request('GET', '/api/v1/chats', {'name': self.chat_name})
            chats = chats_data.get('data', [])

            if chats:
                SimpleLogger.info(f"找到现有聊天助手: {self.chat_name}")
                return chats[0]['id']

            # 创建新聊天助手
            SimpleLogger.info(f"创建新聊天助手: {self.chat_name}")

            # 配置LLM参数
            llm_config = {
                'model_name': getattr(settings, 'RAGFLOW_LLM_MODEL', None),
                'temperature': 0.1,
                'top_p': 0.3,
                'presence_penalty': 0.2,
                'frequency_penalty': 0.7
            }

            # 配置提示词
            prompt_config = {
                'similarity_threshold': 0.2,
                'keywords_similarity_weight': 0.7,
                'top_n': 8,
                'variables': [{'key': 'knowledge', 'optional': True}],
                'empty_response': '抱歉，我无法基于当前知识库生成相关的测试用例。请提供更详细的需求描述。',
                'opener': '🎯 请选择需求，基于您的需求，为您生成测试用例',
                'show_quote': True,
                'prompt': prompt
            }

            dataset_ids = [self.dataset_id] if self.dataset_id else []

            chat_data = self._make_request('POST', '/api/v1/chats', {
                'name': self.chat_name,
                'dataset_ids': dataset_ids,
                'llm': llm_config,
                'prompt': prompt_config
            })
            return chat_data.get('data', {}).get('id', '')

        except Exception as e:
            SimpleLogger.exception(f"聊天助手操作失败: {str(e)}")
            raise Exception(f"聊天助手操作失败: {str(e)}")

    def _create_agent_session(self) -> str:
        """创建agent会话"""
        try:
            session_data = self._make_request('POST', f'/api/v1/agents/{self.agent_id}/sessions', {
                'name': 'AI对话'
            })
            return session_data.get('data', {}).get('id', '')
        except Exception as e:
            SimpleLogger.exception(f"创建Agent会话失败: {str(e)}")
            raise Exception(f"创建Agent会话失败: {str(e)}")

    def _make_request(self, method: str, endpoint: str, data: Dict = None, params: Dict = None, files: Dict = None) -> Dict:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        SimpleLogger.info(f"RAGFlow请求: {method} {url}, 数据: {data}, 参数: {params}")

        try:
            if method.upper() == 'POST':
                if files:
                    # 文件上传请求，不设置Content-Type让requests自动设置
                    headers = {k: v for k, v in self.headers.items() if k != 'Content-Type'}
                    response = requests.post(
                        url,
                        headers=headers,
                        data=data,
                        files=files,
                        params=params,
                        timeout=self.timeout
                    )
                else:
                    response = requests.post(
                        url,
                        headers=self.headers,
                        json=data,
                        params=params,
                        timeout=self.timeout
                    )
            elif method.upper() == 'GET':
                response = requests.get(
                    url,
                    headers=self.headers,
                    params=params or data,
                    timeout=self.timeout
                )
            elif method.upper() == 'DELETE':
                response = requests.delete(
                    url,
                    headers=self.headers,
                    json=data,
                    params=params,
                    timeout=self.timeout
                )
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            SimpleLogger.info(f"RAGFlow响应状态: {response.status_code}")
            response.raise_for_status()

            # 尝试解析JSON响应
            try:
                return response.json()
            except json.JSONDecodeError:
                # 如果不是JSON响应，返回文本内容
                return {'data': response.text}

        except requests.exceptions.RequestException as e:
            SimpleLogger.exception(f"RAGFlow API请求失败: {str(e)}")
            raise Exception(f"RAGFlow API调用失败: {str(e)}")

    def ask(self, question: str, session_id: str = None, stream: bool = False) -> str:
        """
        统一的对话接口，支持chat和agent两种模式

        Args:
            question: 用户问题
            session_id: 会话ID（agent模式使用）
            stream: 是否流式返回

        Returns:
            AI回复内容
        """
        try:
            if self.mode == "chat":
                return self._ask_chat(question, stream)
            elif self.mode == "agent":
                return self._ask_agent(question, session_id, stream)
            else:
                raise Exception(f"不支持的模式: {self.mode}")

        except Exception as e:
            SimpleLogger.exception(f"AI对话失败: {str(e)}")
            return f"对话失败: {str(e)}"

    def _ask_chat(self, question: str, stream: bool = False) -> str:
        """Chat模式对话"""
        if not self.chat_id:
            self._initialize_chat_resources()

        if not self.chat_id:
            raise Exception("Chat助手未初始化")

        # 创建会话
        session_data = self._make_request('POST', f'/api/v1/chats/{self.chat_id}/sessions', {
            'name': 'AI对话'
        })
        session_id = session_data.get('data', {}).get('id', '')

        # 发送消息
        response_data = self._make_request('POST', f'/api/v1/chats/{self.chat_id}/sessions/{session_id}/ask', {
            'question': question,
            'stream': stream
        })

        # 提取回复内容
        return self._extract_response_content(response_data)

    def _ask_agent(self, question: str, session_id: str = None, stream: bool = False) -> str:
        """Agent模式对话"""
        if not self.agent_id:
            raise Exception("Agent ID未配置")

        # 使用指定会话或默认会话
        if not session_id:
            if not self.agent_session_id:
                self._initialize_agent_resources()
            session_id = self.agent_session_id

        if not session_id:
            raise Exception("Agent会话未初始化")

        # 发送消息
        response_data = self._make_request('POST', f'/api/v1/agents/{self.agent_id}/sessions/{session_id}/ask', {
            'question': question,
            'stream': stream
        })

        # 提取回复内容
        return self._extract_response_content(response_data)

    def _extract_response_content(self, response_data: Dict) -> str:
        """提取响应内容"""
        # 尝试多种可能的响应格式
        if 'data' in response_data:
            data = response_data['data']
            if isinstance(data, dict):
                # 尝试获取answer字段
                if 'answer' in data:
                    return data['answer']
                # 尝试获取content字段
                if 'content' in data:
                    return data['content']
                # 尝试获取message字段
                if 'message' in data:
                    return data['message']
            elif isinstance(data, str):
                return data

        # 直接尝试获取answer字段
        if 'answer' in response_data:
            return response_data['answer']

        # 直接尝试获取content字段
        if 'content' in response_data:
            return response_data['content']

        # 如果都没有，返回整个响应的字符串表示
        return str(response_data)
    
    
    def _extract_json_from_response(self, response_text: str) -> Dict:
        """从响应文本中提取JSON数据"""

        # 尝试直接解析JSON
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            pass

        # 尝试从markdown代码块中提取JSON
        import re
        json_pattern = r'```json\s*(.*?)\s*```'
        matches = re.findall(json_pattern, response_text, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        # 尝试从普通代码块中提取JSON
        code_pattern = r'```\s*(.*?)\s*```'
        matches = re.findall(code_pattern, response_text, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        # 如果都失败了，尝试查找JSON对象
        json_obj_pattern = r'\{.*\}'
        matches = re.findall(json_obj_pattern, response_text, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        raise ValueError("无法从响应中提取有效的JSON数据")

    def create_agent_session(self, name: str = None) -> str:
        """
        创建新的Agent会话

        Args:
            name: 会话名称

        Returns:
            会话ID
        """
        if self.mode != "agent":
            raise Exception("只有Agent模式支持创建会话")

        if not self.agent_id:
            raise Exception("Agent ID未配置")

        session_data = self._make_request('POST', f'/api/v1/agents/{self.agent_id}/sessions', {
            'name': name or 'AI对话'
        })
        return session_data.get('data', {}).get('id', '')

    def get_agent_session(self, session_id: str):
        """获取Agent会话"""
        if self.mode != "agent":
            raise Exception("只有Agent模式支持获取会话")

        if not self.agent_id:
            raise Exception("Agent ID未配置")

        session_data = self._make_request('GET', f'/api/v1/agents/{self.agent_id}/sessions/{session_id}')
        return session_data.get('data', {})   
   
    def get_chat_history(self, session_id: str = None, limit: int = 10) -> List[Dict]:
        """获取聊天历史"""
        try:
            if self.mode == "chat":
                if not self.chat_id:
                    self._initialize_chat_resources()

                if not self.chat_id:
                    raise Exception("聊天助手未初始化")

                # 获取会话列表
                sessions_data = self._make_request('GET', f'/api/v1/chats/{self.chat_id}/sessions', {
                    'page_size': limit
                })
                sessions = sessions_data.get('data', [])

                history = []
                for session in sessions:
                    if session_id and session.get('id') != session_id:
                        continue

                    # 获取会话消息
                    messages_data = self._make_request('GET', f'/api/v1/chats/{self.chat_id}/sessions/{session.get("id")}/messages')
                    messages = messages_data.get('data', [])

                    for message in messages:
                        history.append({
                            'session_id': session.get('id'),
                            'session_name': session.get('name'),
                            'role': message.get('role', 'assistant'),
                            'content': message.get('content', ''),
                            'timestamp': message.get('create_time', '')
                        })

                return history

            elif self.mode == "agent":
                if not self.agent_id:
                    raise Exception("Agent ID未配置")

                # 获取agent会话列表
                sessions_data = self._make_request('GET', f'/api/v1/agents/{self.agent_id}/sessions', {
                    'page_size': limit
                })
                sessions = sessions_data.get('data', [])

                history = []
                for session in sessions:
                    if session_id and session.get('id') != session_id:
                        continue

                    # 获取会话消息
                    messages_data = self._make_request('GET', f'/api/v1/agents/{self.agent_id}/sessions/{session.get("id")}/messages')
                    messages = messages_data.get('data', [])

                    for message in messages:
                        history.append({
                            'session_id': session.get('id'),
                            'session_name': session.get('name'),
                            'role': message.get('role', 'assistant'),
                            'content': message.get('content', ''),
                            'timestamp': message.get('create_time', '')
                        })

                return history

        except Exception as e:
            SimpleLogger.exception(f"获取聊天历史失败: {str(e)}")
            return []

    def delete_conversation(self, conversation_id: str) -> Dict:
        """删除会话"""
        try:
            if self.mode == "chat":
                if not self.chat_id:
                    raise Exception("聊天助手未初始化")

                return self._make_request('DELETE', f'/api/v1/chats/{self.chat_id}/sessions/{conversation_id}')

            elif self.mode == "agent":
                if not self.agent_id:
                    raise Exception("Agent ID未配置")

                return self._make_request('DELETE', f'/api/v1/agents/{self.agent_id}/sessions/{conversation_id}')

        except Exception as e:
            SimpleLogger.exception(f"删除会话失败: {str(e)}")
            return {'error': str(e)}
    
    def upload_document(
        self,
        file_path: Union[str, Path],
        dataset_id: Optional[str] = None,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None,
        metadata: Optional[Dict] = None
    ) -> Dict:
        """
        上传文档到知识库

        参数:
            file_path: 要上传的文件路径
            dataset_id: 数据集ID(为空则使用默认)
            chunk_size: 文档分割的块大小(可选)
            chunk_overlap: 文档分割的块重叠大小(可选)
            metadata: 附加到文档的元数据(可选)

        返回:
            包含上传状态和文档ID的API响应
        """
        dataset_id = dataset_id or self.dataset_id
        if not dataset_id:
            raise ValueError("未提供数据集ID且未设置默认值")

        if isinstance(file_path, str):
            file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"文件未找到: {file_path}")

        try:
            data = {}
            if chunk_size is not None:
                data['chunk_size'] = chunk_size
            if chunk_overlap is not None:
                data['chunk_overlap'] = chunk_overlap
            if metadata:
                data['metadata'] = metadata

            files = {
                'file': (file_path.name, open(file_path, 'rb')),
            }

            return self._make_request('POST', f'/api/v1/datasets/{dataset_id}/documents', data=data, files=files)

        except Exception as e:
            SimpleLogger.exception(f"上传文档失败: {str(e)}")
            return {'error': str(e)}
    
    def create_dataset(
        self,
        name: str,
        description: Optional[str] = None,
        embedding_model: Optional[str] = None,
        make_default: bool = False
    ) -> Dict:
        """
        创建新数据集

        参数:
            name: 要创建的数据集名称
            description: 数据集描述(可选)
            embedding_model: 使用的嵌入模型(可选)
            make_default: 是否设置为客户端的默认数据集

        返回:
            包含创建状态的API响应
        """
        data = {'name': name}
        if description:
            data['description'] = description
        if embedding_model:
            data['embedding_model'] = embedding_model

        try:
            response = self._make_request('POST', '/api/v1/datasets', data=data)

            if make_default:
                self.dataset_id = response.get('data', {}).get('id', '')

            return response

        except Exception as e:
            SimpleLogger.exception(f"创建数据集失败: {str(e)}")
            return {'error': str(e)}

    def list_datasets(self) -> List[Dict]:
        """
        列出所有可用数据集

        返回:
            数据集对象列表
        """
        try:
            response = self._make_request('GET', '/api/v1/datasets')
            return response.get('data', [])
        except Exception as e:
            SimpleLogger.exception(f"获取数据集列表失败: {str(e)}")
            return []

    def delete_dataset(self, dataset_id: str) -> Dict:
        """
        删除数据集

        参数:
            dataset_id: 要删除的数据集ID

        返回:
            包含删除状态的API响应
        """
        try:
            return self._make_request('DELETE', f'/api/v1/datasets/{dataset_id}')
        except Exception as e:
            SimpleLogger.exception(f"删除数据集失败: {str(e)}")
            return {'error': str(e)}

    def delete_knowledge_document(self, document_id: str) -> bool:
        """删除知识文档"""
        try:
            if not self.dataset_id:
                self._initialize_resources()

            if not self.dataset_id:
                raise Exception("数据集未初始化")

            self._make_request('DELETE', f'/api/v1/datasets/{self.dataset_id}/documents/{document_id}')
            SimpleLogger.info(f"成功删除知识文档: {document_id}")

            return True

        except Exception as e:
            SimpleLogger.exception(f"删除知识文档失败: {str(e)}")
            return False
    

    def health_check(self) -> Dict[str, Any]:
        """健康检查 - 支持chat和agent两种模式"""
        try:
            # 检查客户端连接
            if self.mode == "chat":
                self._make_request('GET', '/api/v1/datasets', {'page_size': 1})
            else:  # agent模式
                self._make_request('GET', '/api/v1/agents', {'page_size': 1})

            # 检查资源状态
            result = {
                'status': 'healthy',
                'client_connected': True,
                'mode': self.mode,
                'base_url': self.base_url
            }

            if self.mode == "chat":
                result.update({
                    'dataset_status': "OK" if self.dataset_id else "NOT_INITIALIZED",
                    'chat_assistant_status': "OK" if self.chat_id else "NOT_INITIALIZED",
                    'dataset_name': self.dataset_name,
                    'chat_name': self.chat_name
                })
            elif self.mode == "agent":
                result.update({
                    'agent_status': "OK" if self.agent_id else "NOT_CONFIGURED",
                    'agent_session_status': "OK" if self.agent_session_id else "NOT_INITIALIZED",
                    'agent_id': self.agent_id
                })

            return result

        except Exception as e:
            SimpleLogger.exception(f"健康检查失败: {str(e)}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'client_connected': False,
                'mode': self.mode
            }


# 全局RAGFlow客户端实例 - 默认使用chat模式
ragflow_client = RAGFlowClient(mode="chat")

# 预定义的客户端实例
ragflow_chat_client = RAGFlowClient(mode="chat")
ragflow_agent_client = RAGFlowClient(mode="agent")
